{% extends 'main/base.html' %}
{% block title %}Book Search{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>Search Books</h2>
    <form method="get" action="{% url 'book_search' %}">
        <div class="mb-3">
            <input type="text" name="q" value="{{ query }}" class="form-control" placeholder="Search by title, author, or genre">
        </div>
        <button type="submit" class="btn btn-primary">Search</button>
    </form>

    <hr>

    {% if books %}
    {% if books.object_list %}
        <h4>Results:</h4>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>Title</th>
                    <th>Author</th>
                    <th>Genre</th>
                    <th>Publication Year</th>
                    <th>ISBN</th>
                    <th>Total Copies</th>
                    <th>Copies Available</th>
                    <th>Availability</th>
                   
                </tr>
            </thead>
            <tbody>
                {% for book in books %}
                <tr>
                    <td><a href="{% url 'book_detail' book.book_id %}?q={{ query }}"> {{ book.title }} </a></td>
                    <td>{{ book.author.author_name }}</td>
                    <td>{{ book.genre.genre_name }}</td>
                    <td>{{ book.publication_year }}</td>
                    <td>{{ book.isbn }}</td>
                    <td>{{ book.total_copies }}</td>
                    <td>{{ book.copies_available }}</td>
                    <td>{{ book.availability|yesno:"Yes,No" }}</td>
                    
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <nav aria-label="Page navigation" class="mt-3">
            <ul class="pagination">
                {% if books.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?q={{ query }}&page=1">First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?q={{ query }}&page={{ books.previous_page_number }}">Previous</a>
                    </li>
                {% endif %}

                <li class="page-item disabled">
                    <span class="page-link">Page {{ books.number }} of {{ books.paginator.num_pages }}</span>
                </li>

                {% if books.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?q={{ query }}&page={{ books.next_page_number }}">Next</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?q={{ query }}&page={{ books.paginator.num_pages }}">Last</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    {% else %}
        <p>No books found.</p>
    {% endif %}
{% endif %}

{% endblock %}