{% extends 'main/base.html' %}
{% load static %}
{% block title %}{{ book.title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>{{ book.title }}</h2>
    <div class="row mt-3">
        <div class="col-md-4">
            {% if book.cover_image %}
                <img src="{{ book.cover_image.url }}" alt="{{ book.title }}" class="img-fluid">
            {% else %}
                <img src="{% static 'images/default-cover.png' %}" alt="No Cover" class="img-fluid">
            {% endif %}
        </div>
        <div class="col-md-8">
            <table class="table table-bordered">
                <tr>
                    <th>Title</th>
                    <td>{{ book.title }}</td>
                </tr>
                <tr>
                    <th>Author</th>
                    <td>{{ book.author.author_name }}</td>
                </tr>
                <tr>
                    <th>Genre</th>
                    <td>{{ book.genre.genre_name }}</td>
                </tr>
                <tr>
                    <th>Publication Year</th>
                    <td>{{ book.publication_year }}</td>
                </tr>
                <tr>
                    <th>ISBN</th>
                    <td>{{ book.isbn }}</td>
                </tr>
                <tr>
                    <th>Total Copies</th>
                    <td>{{ book.total_copies }}</td>
                </tr>
                <tr>
                    <th>Copies Available</th>
                    <td>{{ book.copies_available }}</td>
                </tr>
                <tr>
                    <th>Availability</th>
                    <td>{{ book.availability|yesno:"Yes,No" }}</td>
                </tr>
                <tr>
                    <th>Description</th>
                    <td>{{ book.description }}</td>
                </tr>
            </table>

                    <a href="{% url 'book_search' %}?q={{ request.GET.q }}" class="btn btn-secondary mt-3">Back to Search</a>
  
        </div>
    </div>
</div>
{% endblock %}
