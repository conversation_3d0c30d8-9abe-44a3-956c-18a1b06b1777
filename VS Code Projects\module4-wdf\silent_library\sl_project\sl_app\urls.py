from django.contrib import admin
from django.urls import path, include
from . import views
from django.contrib.auth import views as auth_views

urlpatterns = [
    # =============================================================================
    # ADMIN SECTION
    # =============================================================================
    path("admin/", admin.site.urls),

    # =============================================================================
    # PUBLIC PAGES
    # =============================================================================
    path('', views.landing_page, name="landing_page"),
    path('home/', views.home_page, name="home_page"),
    path('contact/', views.contact_page, name="contact_page"),
    path('privacy/', views.privacy_page, name="privacy_page"),
    path('sitemap/', views.sitemap_page, name="sitemap_page"),
    path('events/', views.events_page, name="events_page"),

    # =============================================================================
    # AUTHENTICATION & USER MANAGEMENT
    # =============================================================================
    path('login/', views.login_page, name='login'),
    path('logout/', views.logout_page, name='logout'),
    path("signup/", views.signup_page, name="signup"),
    path("signup_success/", views.signup_success_page, name="signup_success"),

    # Password Reset
    path("password_reset/", views.password_reset_page, name="password_reset"),
    path('password_reset_verify/', views.password_reset_verify, name='password_reset_verify'),
    path('password_reset_confirm/', views.password_reset_confirm, name='password_reset_confirm'),

    # =============================================================================
    # USER PROFILE & DASHBOARD
    # =============================================================================
    path('profile/', views.profile_view, name='profile'),
    path('profile/edit/', views.profile_edit, name='edit_profile'),
    path("userprofile/", views.userprofile_page, name="userprofile"),
    path("user_dashboard/", views.user_dashboard_page, name="user_dashboard"),

    # =============================================================================
    # BOOKS & LIBRARY FEATURES
    # =============================================================================
    path('books/', views.books_page, name="books_page"),
    path('book_search/', views.book_search_page, name='book_search'),
    path('advanced_search/', views.advanced_search_page, name="advanced_search"),
    path('book/<int:book_id>/', views.book_detail_page, name='book_detail'),

    # Individual Book Details (Legacy)
    path('book_details_1/', views.book_details_1_page, name="book_details_1"),
    path('book_details_2/', views.book_details_2_page, name="book_details_2"),
    path('book_details_3/', views.book_details_3_page, name="book_details_3"),
]
   