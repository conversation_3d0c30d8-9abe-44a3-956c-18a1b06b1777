<!-- user_dashboard.html -->

{% extends 'main/base.html' %}  
{% load static %}
{% block title %}Login{% endblock %} {% block content %}

<div class="container dashboard-container">
    <!-- Welcome Message -->
    <div class="mb-4 text-center">
      <h2>Welcome, {{ user.username }}!</h2>
    </div>

    <div class="row justify-content-center">
      <!-- Profile Card -->
      <div class="col-md-4 mb-4">
        <div class="card text-center">
          <div class="card-body">
            {% if profile.profile_picture %}
    <img src="{{ profile.profile_picture.url }}" alt="Profile Picture" class="profile-picture mb-3">
{% else %}
     {% if profile.gender == 'M' %}
        <img src="/media/profile_pictures/avatar_male.jpg" alt="Male Avatar" class="profile-picture mb-3">
    {% else %}
        <img src="/media/profile_pictures/avatar_female.jpg" alt="Female Avatar" class="profile-picture mb-3">
    {% endif %}
{% endif %}


            <h5 class="card-title">{{ user.first_name }} {{ user.last_name }}</h5>
            <p class="card-text">{{ user.email }}</p>
            {% comment %} <a href="{% url 'edit_profile' %}" class="btn btn-primary btn-sm">Edit Profile</a> {% endcomment %}
          </div>
        </div>
      </div>

      <!-- Search Card -->
      <div class="col-md-6 mb-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">Search Books</h5>
            <form method="GET" >
              <div class="input-group mb-3">
                <input type="text" class="form-control" name="query" placeholder="Search by title, author, genre">
                <button class="btn btn-success" type="submit">Search</button>
              </div>
            </form>
            <!-- Example: search results display area -->
            {% if search_results %}
              <ul class="list-group mt-3">
                {% for book in search_results %}
                  <li class="list-group-item">
                    {% comment %} <a href="{% url 'book_detail' book.id %}">{{ book.title }}</a> by {{ book.author }} {% endcomment %}
{{ book.title }} by {{ book.author }}

                  </li>
                {% endfor %}
              </ul>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
  {% endblock %}