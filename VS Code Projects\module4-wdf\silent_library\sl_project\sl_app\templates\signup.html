{% extends 'main/base.html' %}
{% block title %}Register{% endblock %}
{% block content %}
<div class="container">
  <div class="signup-card shadow p-4 rounded bg-white mt-4">
    <h3 class="text-center mb-3">New User Registration</h3>
    <p class="text-center mb-4">Fill in the details below to create your account.</p>

    <form method="POST" enctype="multipart/form-data">
      {% csrf_token %}

      <div class="row g-3 mb-3">
        <div class="col-md-6">
          <label class="form-label">Username <span class="text-danger">*</span></label>
          <input type="text" name="username" class="form-control" placeholder="Enter username" required
                 value="{{ form_data.username|default:'' }}">
        </div>
        <div class="col-md-6">
          <label class="form-label">Profile Picture</label>
          <input type="file" name="profile_picture" class="form-control">
        </div>
      </div>

      <div class="row g-3 mb-3">
        <div class="col-md-6">
          <label class="form-label">First Name <span class="text-danger">*</span></label>
          <input type="text" name="first_name" class="form-control" placeholder="Enter first name" required
                 value="{{ form_data.first_name|default:'' }}">
        </div>
        <div class="col-md-6">
          <label class="form-label">Last Name <span class="text-danger">*</span></label>
          <input type="text" name="last_name" class="form-control" placeholder="Enter last name" required
                 value="{{ form_data.last_name|default:'' }}">
        </div>
      </div>

      <div class="row g-3 mb-3">
        <div class="col-md-6">
          <label class="form-label">Password <span class="text-danger">*</span></label>
          <input type="password" name="password" class="form-control" placeholder="Enter password" required>
        </div>
        <div class="col-md-6">
          <label class="form-label">Confirm Password <span class="text-danger">*</span></label>
          <input type="password" name="confirm_password" class="form-control" placeholder="Re-enter password" required>
        </div>
      </div>

      <div class="row g-3 mb-3">
        <div class="col-md-6">
          <label class="form-label">Email <span class="text-danger">*</span></label>
          <input type="email" name="email" class="form-control" placeholder="Enter email" required
                 value="{{ form_data.email|default:'' }}">
        </div>
        <div class="col-md-6">
          <label class="form-label">Bio</label>
          <textarea name="bio" class="form-control" rows="2" placeholder="Tell us something about yourself">{{ form_data.bio|default:'' }}</textarea>
        </div>
      </div>

      <div class="row g-3 mb-4">
        <div class="col-md-6">
          <label class="form-label">Gender <span class="text-danger">*</span></label>
          <select name="gender" class="form-select" required>
            <option value="" disabled {% if not form_data.gender %}selected{% endif %}>-- Select Gender --</option>
            <option value="M" {% if form_data.gender == "M" %}selected{% endif %}>Male</option>
            <option value="F" {% if form_data.gender == "F" %}selected{% endif %}>Female</option>
          </select>
        </div>
      </div>

      <div class="text-center">
        <button type="submit" class="btn btn-primary px-4">Sign Up</button>
      </div>
    </form>
  </div>
</div>
{% endblock %}
