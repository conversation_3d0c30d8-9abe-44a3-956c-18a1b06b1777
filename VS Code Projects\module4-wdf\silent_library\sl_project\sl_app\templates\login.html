<!-- login.html -->

{% extends 'main/base.html' %}  
{% load static %}
{% block title %}Login{% endblock %} {% block content %}


<div class="login-page">
  <div class="login-card text-center">
   <img
      src="https://cdn-icons-png.flaticon.com/512/29/29302.png"
      alt="Silent Library Logo"
      class="logo"
    />
    <h3>Silent Library Login</h3>

    <form method="post" action="{% url 'login' %}">
      {% csrf_token %}
      {% if form.errors %}
        <div class="alert alert-danger">
          Please correct the error(s) below.
        </div>
      {% endif %}

      <div class="mb-3">
        <input
          type="text"
          class="form-control"
          name="username"
          placeholder="Username"
          required
        />
      </div>
      <div class="mb-3">
        <input
          type="password"
          class="form-control"
          name="password"
          placeholder="Password"
          required
        />
      </div>

      <div class="mb-3 text-start">
        <input type="checkbox" id="remember" name="remember" />
        <label for="remember">Remember me</label>
      </div>

      <button type="submit" class="btn btn-custom w-100">Login</button>
      <div class="mt-3">
        <a href="{% url 'password_reset' %}" class="text-decoration-none">Forgot password?</a><br />
        <a href="{% url 'signup' %}" class="text-decoration-none">Create an account</a>
      </div>
    </form>

    {% if messages %}
      <div class="mt-3">
        {% for message in messages %}
          <div class="alert alert-{{ message.tags }}">{{ message }}</div>
        {% endfor %}
      </div>
    {% endif %}
  </div>
</div>
{% endblock %}