from django.shortcuts import get_object_or_404, redirect, render
from django.contrib.auth import authenticate, login, logout
from django.contrib import messages
from django.urls import reverse
from .models  import *
from .forms import *
from django.core.mail import send_mail #email
from django.conf import settings 
from django.contrib.auth.decorators import login_required
from django.utils.crypto import get_random_string
from django.core.paginator import Paginator
from django.db.models import Q

def login_page(request):
    if request.method == "POST":
        username = request.POST.get("username")
        password = request.POST.get("password")

        user = authenticate(request, username=username, password=password)

        if user is not None:
            login(request, user)
            messages.success(request, f"Welcome, {user.username}!")
            return redirect("profile")
        else:
            messages.error(request, "Invalid username or password")

    return render(request, "login.html")
    

def logout_page(request):
    logout(request)
    messages.success(request, "You have been logged out.")
    return redirect("login")


@login_required
def userprofile_page(request):
    user = request.user
    try:
        profile = user.userprofile  # If you have a UserProfile model linked
    except:
        profile = None

    return render(request, "userprofile.html", {"user": user, "profile": profile})

@login_required
def profile_view(request):
    profile, created = UserProfile.objects.get_or_create(user=request.user)
    return render(request, 'profile.html', {
        'profile': profile,
        'user': request.user,
        'edit_mode': False
    })

@login_required
def profile_edit(request):
    profile, created = UserProfile.objects.get_or_create(user=request.user)
    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=profile, user=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, f"User profile updated successfully.")
            return redirect('profile')
    else:
        form = UserProfileForm(instance=profile, user=request.user)

    return render(request, 'profile.html', {
        'profile': profile,
        'user': request.user,
        'form': form,
        'edit_mode': True
    })

@login_required
def user_dashboard_page(request):
    user = request.user
    try:
        profile = user.userprofile
    except UserProfile.DoesNotExist:
        profile = None

    search_results = None
    query = request.GET.get('query')
    if query:
        search_results = Book.objects.filter(
            title__icontains=query
        ) | Book.objects.filter(
            author__icontains=query
        ) | Book.objects.filter(
            genre__icontains=query
        )

    context = {
        'user': user,
        'profile': profile,
        'search_results': search_results
    }
    return render(request, 'user_dashboard.html', context)

def generate_verification_code():
    # Generate a random alphanumeric string
    return get_random_string(length=6, allowed_chars='ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789')

def password_reset_confirm(request):
    user_id = request.session.get('password_reset_user')
    if not user_id:
        messages.error(request, "Session expired. Please request a new password reset.")
        return redirect('password_reset')

    user = User.objects.get(id=user_id)

    if request.method == "POST":
        password = request.POST.get('password')
        confirm = request.POST.get('confirm_password')
        if password != confirm:
            messages.error(request, "Passwords do not match!")
        else:
            user.set_password(password)
            user.save()
            messages.success(request, "Password reset successfully. You can now log in.")

            # Clear session data
            request.session.pop('password_reset_code', None)
            request.session.pop('password_reset_user', None)

            return redirect('login')
    return render(request, 'password_reset_confirm.html')

def password_reset_verify(request):
    if request.method == "POST":
        code_entered = request.POST.get('code')
        code_actual = request.session.get('password_reset_code')
        
        if code_entered == code_actual:
            messages.success(request, "Code verified. You can now set a new password.")
            return redirect('password_reset_confirm')
        else:
            messages.error(request, "Invalid verification code.")
            return redirect('password_reset_verify')
    
    return render(request, 'password_reset_verify.html')


def password_reset_page(request):
    if request.method == "POST":
        email = request.POST.get('email')
        try:
            user = User.objects.get(email=email)
            
            # Generate a random 6-digit numeric code
            code = get_random_string(length=6, allowed_chars='0123456789')
            
            # Store code and user ID in session
            request.session['password_reset_code'] = code
            request.session['password_reset_user'] = user.id
            
            send_mail(
                subject="Password Reset Verification Code",
                message=f"Hi {user.username},\nYour password reset verification code is: {code}\nIt is valid for this session.",
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[email],
            )
            messages.success(request, "Verification code has been sent to your email.")
            return redirect('password_reset_verify')  # page to enter code
        except User.DoesNotExist:
            messages.error(request, "Email address not found.")
            return redirect('password_reset')
    return render(request, 'password_reset.html')

def signup_page(request):
    if request.method == "POST":
        username = request.POST.get("username", "").strip()
        email = request.POST.get("email", "").strip()
        password = request.POST.get("password") or ""
        confirm_password = request.POST.get("confirm_password") or ""
        first_name = request.POST.get("first_name", "").strip()
        last_name = request.POST.get("last_name", "").strip()
        bio = request.POST.get("bio", "").strip()
        gender = request.POST.get("gender", "")
        profile_picture = request.FILES.get("profile_picture")

        # Collect errors
        errors = []
        if not username or not email or not password:
            errors.append("Username, Email, and Password are required.")
        if User.objects.filter(username=username).exists():
            errors.append("Username already exists. Please choose another.")
        if User.objects.filter(email=email).exists():
            errors.append("Email already registered. Try logging in if you already have an account. Else, use a different email.")
        if password != confirm_password:
            errors.append("Passwords do not match.")
        if gender not in ("M", "F"):
            errors.append("Please select a valid gender.")

        if errors:
            for e in errors:
                messages.error(request, e)
            # IMPORTANT: pass posted values back so the template can repopulate
            return render(request, "signup.html", {"form_data": request.POST})

        # Create user
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name,
        )

        # Create profile
        UserProfile.objects.create(
            user=user,
            bio=bio,
            gender=gender,
            profile_picture=profile_picture,
        )

        # Send email (best-effort)
        try:
            send_mail(
                subject="Successful Registration on Silent Library",
                message=(
                    f"Dear {user.first_name},\n\n"
                    f"Thank you for registering on our Silent Library. We are excited to have you as a member.\n\n"
                    f"Thank you.\nSilent Library Team"
                ),
                from_email=f"Silent Library {settings.DEFAULT_FROM_EMAIL}",
                # recipient_list=[email],
                recipient_list=["<EMAIL>"],
                fail_silently=False,
            )
            # messages.success(request, "Registration successful! You can now log in.")
            return redirect("signup_success")  # or your success page
        except Exception as e:
            messages.error(request, f"Your account has been created successfully. However, there is an Error in sending email: {e}.  You still can login to the system using your credentials.")
            return redirect("login")  # or your success page
        

    # GET: initial load (no posted data)
    return render(request, "signup.html", {"form_data": {}})


def book_search_page(request):
    query = request.GET.get('q', '')  # Get search query from input
    books = None  # Default to None if no search

    if query:
        books_list = Book.objects.filter(
            Q(title__icontains=query) |
            Q(author__author_name__icontains=query) |
            Q(genre__genre_name__icontains=query)
        )
        paginator = Paginator(books_list, 10)  # 10 books per page
        page_number = request.GET.get('page')
        books = paginator.get_page(page_number)

    context = {
        'books': books,
        'query': query
    }
    return render(request, 'book_search.html', context)


def book_detail_page(request, book_id):
    book = get_object_or_404(Book, pk=book_id)
    context = {
        'book': book
    }
    return render(request, 'book_detail.html', context)

# Create your views here.
def landing_page(request):
    return render(request, "home.html")

def home_page(request):
    return render(request, "home.html")

def books_page(request):
    return render(request, "books.html")

def events_page(request):
    return render(request, "events.html")

def contact_page(request):
    return render(request, "contact.html")

def privacy_page(request):
    return render(request, "privacy.html")

def sitemap_page(request):
    return render(request, "sitemap.html")

def book_details_1_page(request):
    return render(request, "book_details_1.html")

def book_details_2_page(request):
    return render(request, "book_details_2.html")

def book_details_3_page(request):
    return render(request, "book_details_3.html")

def advanced_search_page(request):
    return render(request, "advanced_search.html")

def signup_success_page(request):
    return render(request, "signup_success.html")
