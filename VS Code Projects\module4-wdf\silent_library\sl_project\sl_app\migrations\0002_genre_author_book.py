# Generated by Django 5.2.5 on 2025-08-21 23:54

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('sl_app', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Genre',
            fields=[
                ('genre_id', models.AutoField(primary_key=True, serialize=False)),
                ('genre_name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Author',
            fields=[
                ('author_id', models.AutoField(primary_key=True, serialize=False)),
                ('author_name', models.CharField(db_index=True, max_length=100)),
                ('author_biography', models.TextField(blank=True, null=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('nationality', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'indexes': [models.Index(fields=['author_name'], name='sl_app_auth_author__7178b9_idx')],
            },
        ),
        migrations.CreateModel(
            name='Book',
            fields=[
                ('book_id', models.AutoField(primary_key=True, serialize=False)),
                ('title', models.CharField(db_index=True, max_length=200)),
                ('publication_year', models.PositiveSmallIntegerField()),
                ('isbn', models.CharField(max_length=20, unique=True)),
                ('description', models.TextField()),
                ('total_copies', models.PositiveIntegerField()),
                ('copies_available', models.PositiveIntegerField()),
                ('availability', models.BooleanField(default=True)),
                ('cover_image', models.ImageField(blank=True, null=True, upload_to='books/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='books', to='sl_app.author')),
                ('genre', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='books', to='sl_app.genre')),
            ],
            options={
                'indexes': [models.Index(fields=['title'], name='sl_app_book_title_1f3fac_idx'), models.Index(fields=['publication_year'], name='sl_app_book_publica_8df6a3_idx')],
            },
        ),
    ]
