# Generated by Django 5.2.5 on 2025-08-21 23:59

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('sl_app', '0002_genre_author_book'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='book',
            name='author',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='books', to='sl_app.author'),
        ),
        migrations.AlterField(
            model_name='book',
            name='genre',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='books', to='sl_app.genre'),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.RESTRICT, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='book',
            index=models.Index(fields=['isbn'], name='sl_app_book_isbn_830edd_idx'),
        ),
        migrations.AddIndex(
            model_name='genre',
            index=models.Index(fields=['genre_name'], name='sl_app_genr_genre_n_49dad5_idx'),
        ),
    ]
