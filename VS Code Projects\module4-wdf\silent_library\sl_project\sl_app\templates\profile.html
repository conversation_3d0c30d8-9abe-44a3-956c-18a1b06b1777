{% extends 'main/base.html' %}
{% load static %}
{% block title %}User Profile{% endblock %}

{% block content %}
<div class="container">
  <div class="profile-card">
    <div class="profile-header">
      {% if profile.profile_picture %}
        <img src="{{ profile.profile_picture.url }}" class="profile-picture mb-3" alt="Profile Picture">
      {% else %}
        {% if profile.gender == 'M' %}
          <img src="/media/profile_pictures/avatar_male.jpg" alt="Male Avatar" class="profile-picture mb-3">
        {% else %}
          <img src="/media/profile_pictures/avatar_female.jpg" alt="Female Avatar" class="profile-picture mb-3">
        {% endif %}
      {% endif %}
      <h3>{{ user.get_full_name|default:user.username }}</h3>
      <p class="text-muted">@{{ user.username }}</p>
    </div>

    <hr>

    {% if edit_mode %}
      <form method="POST" enctype="multipart/form-data">
        {% csrf_token %}
        <ul class="list-group list-group-flush">
          <li class="list-group-item"><strong>First Name:</strong> {{ form.first_name }}</li>
          <li class="list-group-item"><strong>Last Name:</strong> {{ form.last_name }}</li>
          <li class="list-group-item"><strong>Email:</strong> {{ form.email }}</li>
          <li class="list-group-item"><strong>Bio:</strong> {{ form.bio }}</li>
          <li class="list-group-item"><strong>Profile Picture:</strong> {{ form.profile_picture }}</li>
          <li class="list-group-item"><strong>Gender:</strong> {{ form.gender }}</li>
        </ul>
        <div class="mt-4 text-center">
          <button type="submit" class="btn btn-primary">Save Changes</button>
          <a href="{% url 'profile' %}" class="btn btn-secondary">Cancel</a>
        </div>
      </form>
    {% else %}
      <ul class="list-group list-group-flush">
        <li class="list-group-item"><strong>First Name:</strong> {{ user.first_name }}</li>
        <li class="list-group-item"><strong>Last Name:</strong> {{ user.last_name }}</li>
        <li class="list-group-item"><strong>Email:</strong> {{ user.email }}</li>
        <li class="list-group-item"><strong>Bio:</strong> {{ profile.bio }}</li>
        <li class="list-group-item"><strong>Gender:</strong> {{ profile.gender }}</li>
      </ul>
      <div class="mt-4 text-center">
        <a href="{% url 'edit_profile' %}" class="btn btn-primary">Edit Profile</a>
        <a href="{% url 'logout' %}" class="btn btn-outline-danger">Logout</a>
      </div>
    {% endif %}
  </div>
</div>
{% endblock %}
