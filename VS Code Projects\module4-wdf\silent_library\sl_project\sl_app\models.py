from datetime import timezone
from django.db import models
from django.contrib.auth.models import *

# Create your models here.
class UserProfile(models.Model):
    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
    ]

    user = models.OneToOneField(User, on_delete=models.RESTRICT)
    bio = models.TextField(blank=True)
    profile_picture = models.ImageField(upload_to='profile_pictures/', blank=True, null=True)
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, blank=True)
    
def __str__(self):
        return self.user.username

class Author(models.Model):
    author_id = models.AutoField(primary_key=True)
    author_name = models.Char<PERSON>ield(max_length=100, db_index=True)
    author_biography = models.TextField(blank=True, null=True)
    date_of_birth = models.DateField(blank=True, null=True)
    nationality = models.CharField(max_length=100, blank=True, null=True)

    created_at = models.DateTimeField( default=timezone.now, editable=False)
    updated_at = models.DateTimeField( default=timezone.now)

    class Meta:
        indexes = [
            models.Index(fields=['author_name']),
        ]

    def save(self, *args, **kwargs):
        if not self.created_at:
            self.created_at = timezone.now()  # set once when object is created
        self.updated_at = timezone.now()      # update every save
        super().save(*args, **kwargs)
        
    def __str__(self):
        return self.author_name


class Genre(models.Model):
    genre_id = models.AutoField(primary_key=True)
    genre_name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)

    created_at = models.DateTimeField( default=timezone.now,editable=False)
    updated_at = models.DateTimeField(default=timezone.now)
    
    
    
    class Meta:
        indexes = [
            models.Index(fields=['genre_name']),
           
        ]

    def save(self, *args, **kwargs):
        if not self.created_at:
            self.created_at = timezone.now()  # set once when object is created
        self.updated_at = timezone.now()      # update every save
        super().save(*args, **kwargs)
        
    def __str__(self):
        return self.genre_name


class Book(models.Model):
    book_id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=200, db_index=True)
    author = models.ForeignKey(Author, on_delete=models.RESTRICT, related_name='books')
    publication_year = models.PositiveSmallIntegerField()
    isbn = models.CharField(max_length=20, unique=True)
    description = models.TextField()
    total_copies = models.PositiveIntegerField()
    copies_available = models.PositiveIntegerField()
    genre = models.ForeignKey(Genre, on_delete=models.RESTRICT, related_name='books')
    availability = models.BooleanField(default=True)
    cover_image = models.ImageField(upload_to='books/', blank=True, null=True)

    created_at = models.DateTimeField( default=timezone.now,editable=False)
    updated_at = models.DateTimeField( default=timezone.now)

    class Meta:
        indexes = [
            models.Index(fields=['title']),
            models.Index(fields=['isbn']),
            models.Index(fields=['publication_year']),
        ]
        
    def save(self, *args, **kwargs):
        if not self.created_at:
            self.created_at = timezone.now()  # set once when object is created
        self.updated_at = timezone.now()      # update every save
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.title} ({self.publication_year})"